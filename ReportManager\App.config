﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System">
      <section name="DevExpress.LookAndFeel.Design.AppSettings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false"/>
    </sectionGroup>
  </configSections>
  
  <!-- 数据库连接字符串 -->
  <!-- 注意：请根据实际环境修改以下参数：
       - Server: 数据库服务器地址
       - Database: 数据库名称
       - User ID: 数据库用户名
       - Password: 数据库密码（请修改为实际密码）
       - 系统会自动检测两个数据库连接，优先使用第一个可用的连接
  -->
  <connectionStrings>
    <!-- 主数据库连接 -->
    <add name="ReportDB_Primary" connectionString="Server=192.168.20.85;Database=DaPeng_IOServer;User ID=sa;Password=********;Connect Timeout=30;Encrypt=False;TrustServerCertificate=False;ApplicationIntent=ReadWrite;MultiSubnetFailover=False;" />
    <!-- 备用数据库连接 -->
    <add name="ReportDB_Secondary" connectionString="Server=.;Database=DaPeng_IOServer;User ID=sa;Password=********;Connect Timeout=30;Encrypt=False;TrustServerCertificate=False;ApplicationIntent=ReadWrite;MultiSubnetFailover=False;" />
  </connectionStrings>
  
  <!-- 应用程序设置 -->
  <appSettings>
    <!-- 报告根目录配置 - 请根据实际情况修改路径 -->
    <!-- 主报告根目录：优先使用此路径 -->
    <add key="ReportRootPath" value="Y:\" />
    <!-- 备用报告根目录：当主路径不可用时自动切换到此路径 -->
    <add key="ReportRootPathBackup" value="Z:\" />
    <!-- 数据库表名 -->
    <add key="SelectionTableName" value="Selection_Table" />
    <!-- 数据库连接检测超时时间（秒）- 用于检测数据库是否可用 -->
    <add key="DatabaseTestTimeout" value="3" />
    <!-- 报告类型配置（格式：显示名称:文件夹名称，用分号分隔） -->
    <add key="ReportTypes" value="小时报告:HourlyReport;日报告:DailyReport;周报告:WeeklyReport;声速核查报告:SOSCheckReport;流量核查报告:FlowCheckReport" />
    <!-- 归档配置 -->
    <!-- 归档阈值月数：比当前日期早多少个月的报告会被归档（默认：3个月） -->
    <add key="ArchiveThresholdMonths" value="3" />
  </appSettings>
  
  <applicationSettings>
    <DevExpress.LookAndFeel.Design.AppSettings>
      <setting name="DPIAwarenessMode" serializeAs="String">
        <value>System</value>
      </setting>
      <setting name="RegisterBonusSkins" serializeAs="String">
        <value>True</value>
      </setting>
    </DevExpress.LookAndFeel.Design.AppSettings>
  </applicationSettings>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8"/>
  </startup>
</configuration>
