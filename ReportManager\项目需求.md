现在需要在已有的winform项目中实现如下功能：
1.项目基于DevExpress winform，.net framwork 4.8，已经有一个form和三个combox控件，两个date控件，一个按钮控件，一个gridcontrol控件；
2.第一个combobox控件用来在窗口启动时，显示从数据库的selection_table里获取所有的station名，注意去掉重复的站名；
第二个combox控件用来在用户在第一个combobox里选择好station后，显示这个station包含的所有flowcomputertag列表，注意同样去掉重复值；
第三个combox控件用来选择报告类型，为固定列表，包括：小时报告，日报告，周报告等
两个date控件用来让用户选择一个日期间隔；
按钮用来根据选择的station和flowcomputertag，找到指定的报告目录，再根据日期间隔，筛选出创建时间是这个时间间隔内的所有PDF和excel文件，显示到gridcontrol控件里，一列是文件名，一列是创建时间；
3.报告路径组成如下：
报告根目录+站名+流量计算机名+报告类型
例如：报告根目录为"D:\Report\"，站名为"东部电厂"，流量计算机名为"FC_11670A"，报告类型为"小时报告"，则实际报告路径为：
D:\Report\东部电厂\FC-11670A\HourlyReport\
程序需要在用户点击按钮后列出这个路径下文件创建时间符合要求的所有PDF和excel文件到gridcontrol控件里
4.报告根目录、数据库连接参数(包括表名selection_table)和报告类型列表，可以放到项目的配置文件里，以便现场修改。


